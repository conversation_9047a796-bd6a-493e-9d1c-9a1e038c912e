#!/usr/bin/env python3
"""
CHM格式转换工具
支持将CHM文件转换为PDF、EPUB、MOBI等格式
支持多并发处理
"""

import os
import sys
import tempfile
import shutil
import subprocess
import logging
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional
import click
from tqdm import tqdm

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('chm_converter.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class CHMExtractor:
    """CHM文件提取器"""
    
    def __init__(self, chm_path: str):
        self.chm_path = Path(chm_path)
        self.temp_dir = None
        
    def extract(self) -> Optional[Path]:
        """提取CHM文件内容到临时目录"""
        try:
            self.temp_dir = Path(tempfile.mkdtemp(prefix='chm_extract_'))
            logger.info(f"正在提取CHM文件: {self.chm_path}")
            
            # 尝试使用7zip提取
            if self._extract_with_7zip():
                return self.temp_dir
                
            # 尝试使用hh.exe提取 (Windows)
            if sys.platform == 'win32' and self._extract_with_hh():
                return self.temp_dir
                
            logger.error("无法提取CHM文件，请确保安装了7zip或在Windows系统上")
            return None
            
        except Exception as e:
            logger.error(f"提取CHM文件时出错: {e}")
            return None
    
    def _extract_with_7zip(self) -> bool:
        """使用7zip提取CHM文件"""
        try:
            # 尝试不同的7zip命令
            commands = ['7z', '7za', '7zip']
            
            for cmd in commands:
                try:
                    result = subprocess.run([
                        cmd, 'x', str(self.chm_path), 
                        f'-o{self.temp_dir}', '-y'
                    ], capture_output=True, text=True, check=True)
                    
                    if result.returncode == 0:
                        logger.info("使用7zip成功提取CHM文件")
                        return True
                        
                except (subprocess.CalledProcessError, FileNotFoundError):
                    continue
                    
            return False
            
        except Exception as e:
            logger.error(f"7zip提取失败: {e}")
            return False
    
    def _extract_with_hh(self) -> bool:
        """使用Windows hh.exe提取CHM文件"""
        try:
            # Windows HTML Help Workshop的反编译命令
            result = subprocess.run([
                'hh', '-decompile', str(self.temp_dir), str(self.chm_path)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("使用hh.exe成功提取CHM文件")
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"hh.exe提取失败: {e}")
            return False
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            logger.info("已清理临时文件")


class BaseConverter:
    """转换器基类"""
    
    def __init__(self, source_dir: Path, output_dir: Path):
        self.source_dir = source_dir
        self.output_dir = output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def convert(self, output_name: str) -> bool:
        """转换方法，子类需要实现"""
        raise NotImplementedError
    
    def _find_index_file(self) -> Optional[Path]:
        """查找索引文件"""
        possible_names = ['index.html', 'index.htm', 'default.html', 'default.htm']
        
        for name in possible_names:
            index_file = self.source_dir / name
            if index_file.exists():
                return index_file
                
        # 如果没找到，返回第一个HTML文件
        html_files = list(self.source_dir.glob('*.html')) + list(self.source_dir.glob('*.htm'))
        if html_files:
            return html_files[0]
            
        return None


class PDFConverter(BaseConverter):
    """PDF转换器"""
    
    def convert(self, output_name: str) -> bool:
        try:
            from weasyprint import HTML, CSS
            
            index_file = self._find_index_file()
            if not index_file:
                logger.error("未找到HTML索引文件")
                return False
            
            output_path = self.output_dir / f"{output_name}.pdf"
            
            # 转换为PDF
            html_doc = HTML(filename=str(index_file))
            html_doc.write_pdf(str(output_path))
            
            logger.info(f"PDF转换完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"PDF转换失败: {e}")
            return False


class EPUBConverter(BaseConverter):
    """EPUB转换器"""
    
    def convert(self, output_name: str) -> bool:
        try:
            from ebooklib import epub
            from bs4 import BeautifulSoup
            
            book = epub.EpubBook()
            book.set_identifier('chm_converted')
            book.set_title(output_name)
            book.set_language('zh')
            book.add_author('CHM Converter')
            
            # 收集所有HTML文件
            html_files = list(self.source_dir.glob('*.html')) + list(self.source_dir.glob('*.htm'))
            
            chapters = []
            for i, html_file in enumerate(html_files):
                with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 创建章节
                chapter = epub.EpubHtml(
                    title=f'Chapter {i+1}',
                    file_name=f'chapter_{i+1}.xhtml',
                    lang='zh'
                )
                chapter.content = content
                book.add_item(chapter)
                chapters.append(chapter)
            
            # 添加目录
            book.toc = chapters
            book.add_item(epub.EpubNcx())
            book.add_item(epub.EpubNav())
            
            # 设置spine
            book.spine = ['nav'] + chapters
            
            output_path = self.output_dir / f"{output_name}.epub"
            epub.write_epub(str(output_path), book)
            
            logger.info(f"EPUB转换完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"EPUB转换失败: {e}")
            return False


class MOBIConverter(BaseConverter):
    """MOBI转换器 (需要Calibre)"""
    
    def convert(self, output_name: str) -> bool:
        try:
            # 首先转换为EPUB
            epub_converter = EPUBConverter(self.source_dir, self.output_dir)
            if not epub_converter.convert(output_name):
                return False
            
            epub_path = self.output_dir / f"{output_name}.epub"
            mobi_path = self.output_dir / f"{output_name}.mobi"
            
            # 使用Calibre转换EPUB到MOBI
            result = subprocess.run([
                'ebook-convert', str(epub_path), str(mobi_path)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"MOBI转换完成: {mobi_path}")
                # 删除临时EPUB文件
                epub_path.unlink()
                return True
            else:
                logger.error(f"MOBI转换失败: {result.stderr}")
                return False
                
        except FileNotFoundError:
            logger.error("未找到Calibre，请安装Calibre以支持MOBI转换")
            return False
        except Exception as e:
            logger.error(f"MOBI转换失败: {e}")
            return False


class ConversionManager:
    """转换管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.converters = {
            'pdf': PDFConverter,
            'epub': EPUBConverter,
            'mobi': MOBIConverter
        }
    
    def convert_formats(self, source_dir: Path, output_dir: Path, 
                       formats: List[str], output_name: str) -> Dict[str, bool]:
        """并发转换多种格式"""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交转换任务
            future_to_format = {}
            
            for fmt in formats:
                if fmt.lower() in self.converters:
                    converter_class = self.converters[fmt.lower()]
                    converter = converter_class(source_dir, output_dir)
                    future = executor.submit(converter.convert, output_name)
                    future_to_format[future] = fmt
                else:
                    logger.warning(f"不支持的格式: {fmt}")
                    results[fmt] = False
            
            # 收集结果
            with tqdm(total=len(future_to_format), desc="转换进度") as pbar:
                for future in as_completed(future_to_format):
                    fmt = future_to_format[future]
                    try:
                        results[fmt] = future.result()
                    except Exception as e:
                        logger.error(f"{fmt}转换出错: {e}")
                        results[fmt] = False
                    pbar.update(1)
        
        return results


@click.command()
@click.argument('chm_file', type=click.Path(exists=True))
@click.option('--formats', '-f', multiple=True, default=['pdf'], 
              help='要转换的格式 (pdf, epub, mobi)')
@click.option('--output', '-o', default='./output', 
              help='输出目录')
@click.option('--threads', '-t', default=4, 
              help='并发线程数')
@click.option('--name', '-n', default=None, 
              help='输出文件名（不含扩展名）')
def main(chm_file, formats, output, threads, name):
    """CHM格式转换工具"""
    
    chm_path = Path(chm_file)
    output_dir = Path(output)
    
    # 确定输出文件名
    if not name:
        name = chm_path.stem
    
    logger.info(f"开始转换CHM文件: {chm_path}")
    logger.info(f"目标格式: {', '.join(formats)}")
    logger.info(f"输出目录: {output_dir}")
    
    # 提取CHM文件
    extractor = CHMExtractor(chm_file)
    source_dir = extractor.extract()
    
    if not source_dir:
        logger.error("CHM文件提取失败")
        return
    
    try:
        # 转换格式
        manager = ConversionManager(max_workers=threads)
        results = manager.convert_formats(source_dir, output_dir, formats, name)
        
        # 显示结果
        logger.info("转换结果:")
        for fmt, success in results.items():
            status = "成功" if success else "失败"
            logger.info(f"  {fmt.upper()}: {status}")
        
        successful_count = sum(results.values())
        logger.info(f"转换完成: {successful_count}/{len(results)} 个格式转换成功")
        
    finally:
        # 清理临时文件
        extractor.cleanup()


if __name__ == '__main__':
    main()
