@echo off
echo 正在安装CHM转换工具依赖...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 升级pip
echo 正在升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 正在安装Python依赖包...
pip install -r requirements.txt

REM 检查7zip是否可用
7z >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到7zip，建议安装7zip以支持CHM文件提取
    echo 下载地址: https://www.7-zip.org/
)

REM 检查Calibre是否可用（用于MOBI转换）
ebook-convert --version >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到Calibre，MOBI格式转换将不可用
    echo 下载地址: https://calibre-ebook.com/
)

echo.
echo 安装完成！
echo.
echo 使用方法:
echo   python chm_converter.py your_file.chm -f pdf epub mobi -o output_folder
echo.
pause
