# CHM格式转换工具

一个多并发的、精简的CHM格式转换工具，支持将CHM文件转换为PDF、EPUB、MOBI等格式。

## 特性

- 🚀 **多并发处理** - 支持同时转换多种格式，提高效率
- 📚 **多格式支持** - 支持PDF、EPUB、MOBI格式输出
- 🔧 **精简设计** - 代码简洁，依赖最少
- 📊 **进度显示** - 实时显示转换进度
- 📝 **详细日志** - 完整的转换日志记录
- 🖥️ **跨平台** - 支持Windows、Linux、macOS

## 安装

### 1. 安装Python依赖

运行安装脚本（Windows）：
```bash
install.bat
```

或手动安装：
```bash
pip install -r requirements.txt
```

### 2. 安装外部工具

**必需工具（用于CHM提取）：**
- [7-Zip](https://www.7-zip.org/) - 推荐，跨平台支持
- Windows系统可使用内置的hh.exe

**可选工具（用于MOBI转换）：**
- [Calibre](https://calibre-ebook.com/) - 用于EPUB到MOBI转换

## 使用方法

### 基本用法

```bash
# 转换为PDF格式
python chm_converter.py input.chm

# 转换为多种格式
python chm_converter.py input.chm -f pdf epub mobi

# 指定输出目录和文件名
python chm_converter.py input.chm -f pdf epub -o ./output -n my_book
```

### 命令行参数

- `chm_file` - 输入的CHM文件路径
- `-f, --formats` - 要转换的格式列表 (pdf, epub, mobi)，默认为pdf
- `-o, --output` - 输出目录，默认为./output
- `-t, --threads` - 并发线程数，默认为4
- `-n, --name` - 输出文件名（不含扩展名），默认使用CHM文件名

### 使用示例

```bash
# 示例1: 转换为PDF
python chm_converter.py TeklaOpenAPI_Reference.chm

# 示例2: 转换为多种格式，使用8个线程
python chm_converter.py TeklaOpenAPI_Reference.chm -f pdf epub mobi -t 8

# 示例3: 指定输出目录和文件名
python chm_converter.py TeklaOpenAPI_Reference.chm -f pdf epub -o ./books -n tekla_api_reference
```

## 输出格式说明

### PDF
- 使用WeasyPrint引擎转换
- 保持原有的HTML样式和布局
- 适合打印和阅读

### EPUB
- 标准的电子书格式
- 支持大多数电子书阅读器
- 可重排版，适合不同屏幕尺寸

### MOBI
- Amazon Kindle专用格式
- 需要安装Calibre
- 通过EPUB中间格式转换

## 技术架构

```
chm_converter.py
├── CHMExtractor      # CHM文件提取器
├── BaseConverter     # 转换器基类
├── PDFConverter      # PDF转换器
├── EPUBConverter     # EPUB转换器
├── MOBIConverter     # MOBI转换器
└── ConversionManager # 并发管理器
```

## 依赖说明

- **weasyprint** - PDF转换引擎
- **ebooklib** - EPUB处理库
- **beautifulsoup4** - HTML解析
- **lxml** - XML/HTML解析器
- **tqdm** - 进度条显示
- **click** - 命令行界面

## 故障排除

### CHM文件无法提取
1. 确保安装了7-Zip
2. 检查CHM文件是否损坏
3. 在Windows上可尝试使用hh.exe

### PDF转换失败
1. 检查HTML文件是否包含有效内容
2. 确保WeasyPrint依赖已正确安装

### MOBI转换失败
1. 确保安装了Calibre
2. 检查ebook-convert命令是否在PATH中

### 内存不足
1. 减少并发线程数 (`-t` 参数)
2. 处理大文件时建议使用较少线程

## 日志文件

程序运行时会生成 `chm_converter.log` 日志文件，包含详细的转换过程信息，便于问题诊断。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
