#!/usr/bin/env python3
"""
CHM转换工具安装脚本
支持uv和pip两种安装方式
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, shell=False):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, 
            shell=shell, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr
    except FileNotFoundError:
        return False, "命令未找到"


def check_python():
    """检查Python版本"""
    print("检查Python版本...")
    success, output = run_command([sys.executable, "--version"])
    if success:
        print(f"✓ {output.strip()}")
        return True
    else:
        print("✗ Python未找到")
        return False


def install_uv():
    """安装uv包管理器"""
    print("检查uv包管理器...")
    success, _ = run_command(["uv", "--version"])
    
    if success:
        print("✓ uv已安装")
        return True
    
    print("正在安装uv...")
    success, output = run_command([sys.executable, "-m", "pip", "install", "uv"])
    
    if success:
        print("✓ uv安装成功")
        return True
    else:
        print(f"✗ uv安装失败: {output}")
        return False


def install_with_uv():
    """使用uv安装依赖"""
    print("使用uv安装依赖包...")
    # 使用--system标志安装到系统环境
    success, output = run_command(["uv", "pip", "install", "--system", "-r", "requirements.txt"])

    if success:
        print("✓ 依赖包安装成功")
        return True
    else:
        print(f"✗ uv安装失败: {output}")
        return False


def install_with_pip():
    """使用pip安装依赖"""
    print("使用pip安装依赖包...")
    
    # 升级pip
    print("升级pip...")
    run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
    
    # 安装依赖
    success, output = run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
    
    if success:
        print("✓ 依赖包安装成功")
        return True
    else:
        print(f"✗ pip安装失败: {output}")
        return False


def check_external_tools():
    """检查外部工具"""
    print("\n检查外部工具...")
    
    # 检查7zip
    tools_to_check = ["7z", "7za", "7zip"]
    zip_found = False
    
    for tool in tools_to_check:
        success, _ = run_command([tool], shell=True)
        if success or "Usage" in str(_):  # 7zip通常会显示用法信息
            print(f"✓ 找到7zip: {tool}")
            zip_found = True
            break
    
    if not zip_found:
        print("⚠ 未找到7zip，建议安装以支持CHM文件提取")
        print("  下载地址: https://www.7-zip.org/")
    
    # 检查Calibre (用于MOBI转换)
    success, _ = run_command(["ebook-convert", "--version"])
    if success:
        print("✓ 找到Calibre")
    else:
        print("⚠ 未找到Calibre，MOBI格式转换将不可用")
        print("  下载地址: https://calibre-ebook.com/")


def main():
    """主函数"""
    print("CHM转换工具安装程序")
    print("=" * 40)
    
    # 检查Python
    if not check_python():
        print("请先安装Python 3.7+")
        return False
    
    # 检查requirements.txt是否存在
    if not Path("requirements.txt").exists():
        print("✗ 未找到requirements.txt文件")
        return False
    
    # 尝试使用uv安装
    if install_uv():
        if install_with_uv():
            success = True
        else:
            print("uv安装失败，尝试使用pip...")
            success = install_with_pip()
    else:
        print("使用传统pip安装...")
        success = install_with_pip()
    
    if not success:
        print("✗ 依赖安装失败")
        return False
    
    # 检查外部工具
    check_external_tools()
    
    print("\n" + "=" * 40)
    print("✓ 安装完成！")
    print("\n使用方法:")
    print("  python chm_converter.py your_file.chm -f pdf epub mobi -o output_folder")
    print("\n示例:")
    print("  python chm_converter.py TeklaOpenAPI_Reference.chm")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n安装被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"安装过程中出现错误: {e}")
        sys.exit(1)
