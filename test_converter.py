#!/usr/bin/env python3
"""
CHM转换工具测试脚本
"""

import sys
import os
from pathlib import Path

def test_basic_functionality():
    """测试基本功能"""
    print("CHM转换工具测试")
    print("=" * 40)
    
    # 检查CHM文件是否存在
    chm_file = Path("TeklaOpenAPI_Reference.chm")
    if chm_file.exists():
        print(f"✓ 找到CHM文件: {chm_file}")
        print(f"  文件大小: {chm_file.stat().st_size / 1024 / 1024:.2f} MB")
    else:
        print("✗ 未找到CHM文件")
        return False
    
    # 检查Python版本
    print(f"✓ Python版本: {sys.version}")
    
    # 检查依赖是否可用
    dependencies = [
        ('click', '命令行界面'),
        ('tqdm', '进度条'),
        ('pathlib', '路径处理'),
        ('concurrent.futures', '并发处理'),
        ('tempfile', '临时文件'),
        ('subprocess', '子进程'),
        ('logging', '日志记录')
    ]
    
    print("\n检查基础依赖:")
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} - {desc}")
        except ImportError:
            print(f"✗ {dep} - {desc} (缺失)")
    
    # 检查可选依赖
    print("\n检查转换依赖:")
    optional_deps = [
        ('weasyprint', 'PDF转换'),
        ('ebooklib', 'EPUB转换'),
        ('beautifulsoup4', 'HTML解析'),
        ('lxml', 'XML解析')
    ]
    
    for dep, desc in optional_deps:
        try:
            __import__(dep)
            print(f"✓ {dep} - {desc}")
        except ImportError:
            print(f"⚠ {dep} - {desc} (未安装)")
    
    # 检查外部工具
    print("\n检查外部工具:")
    import subprocess
    
    # 检查7zip
    zip_tools = ['7z', '7za', '7zip']
    zip_found = False
    
    for tool in zip_tools:
        try:
            result = subprocess.run([tool], capture_output=True, text=True, timeout=5)
            print(f"✓ {tool} - CHM提取工具")
            zip_found = True
            break
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError):
            continue
    
    if not zip_found:
        print("⚠ 7zip - CHM提取工具 (未找到)")
    
    # 检查Calibre
    try:
        result = subprocess.run(['ebook-convert', '--version'], capture_output=True, text=True, timeout=5)
        print("✓ Calibre - MOBI转换工具")
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError):
        print("⚠ Calibre - MOBI转换工具 (未找到)")
    
    print("\n" + "=" * 40)
    print("测试完成！")
    
    return True

def show_usage():
    """显示使用说明"""
    print("\n使用说明:")
    print("1. 确保安装了所有依赖:")
    print("   python install.py")
    print("   或者: uv pip install --system -r requirements.txt")
    print()
    print("2. 基本使用:")
    print("   python chm_converter.py TeklaOpenAPI_Reference.chm")
    print()
    print("3. 转换多种格式:")
    print("   python chm_converter.py TeklaOpenAPI_Reference.chm -f pdf epub mobi")
    print()
    print("4. 指定输出目录:")
    print("   python chm_converter.py TeklaOpenAPI_Reference.chm -o ./output")
    print()
    print("5. 查看帮助:")
    print("   python chm_converter.py --help")

if __name__ == "__main__":
    try:
        test_basic_functionality()
        show_usage()
    except KeyboardInterrupt:
        print("\n测试被用户取消")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
